package ottopay

import (
	"context"
	"fmt"
	"testing"
	"time"
)

// MockBusinessHandler implements BusinessLogicHandler for testing
type MockBusinessHandler struct {
	validateCredentialsFunc func(ctx context.Context, username, password string) (UserData, error)
	handleInquiryFunc       func(ctx context.Context, req InquiryRequest) (InquiryData, error)
	handlePaymentFunc       func(ctx context.Context, req PaymentRequest) (PaymentData, error)
}

func (m *MockBusinessHandler) ValidateCredentials(ctx context.Context, username, password string) (UserData, error) {
	if m.validateCredentialsFunc != nil {
		return m.validateCredentialsFunc(ctx, username, password)
	}

	// Validate that both username and password are provided
	if username == "" || password == "" {
		return UserData{}, fmt.Errorf("username and password are required")
	}

	return UserData{ID: "test-id", Username: username, IsValid: true}, nil
}

func (m *MockBusinessHandler) HandleInquiry(ctx context.Context, req InquiryRequest) (InquiryData, error) {
	if m.handleInquiryFunc != nil {
		return m.handleInquiryFunc(ctx, req)
	}
	return InquiryData{
		CustomerName: "Test Customer",
		TotalAmount:  150000.00,
		CurrencyCode: CurrencyIDR,
		IsValid:      true,
	}, nil
}

func (m *MockBusinessHandler) HandlePayment(ctx context.Context, req PaymentRequest) (PaymentData, error) {
	if m.handlePaymentFunc != nil {
		return m.handlePaymentFunc(ctx, req)
	}

	// Validate currency code
	if req.CurrencyCode != CurrencyIDR && req.CurrencyCode != CurrencyUSD {
		return PaymentData{
			IsSuccess:    false,
			ErrorMessage: "Unsupported currency",
		}, nil
	}

	return PaymentData{
		IsSuccess:    true,
		CustomerName: "Test Customer",
		TotalAmount:  150000.00,
		PaidAmount:   150000.00,
		CurrencyCode: req.CurrencyCode,
	}, nil
}

// Test helper function to create test config
func createTestConfig() Config {
	return Config{
		JWTSecret:     "test-secret-key",
		TokenDuration: 24 * time.Hour,
		BankPrefixes: map[string]string{
			ChannelMandiri: "71101",
			ChannelBRI:     "15772",
			ChannelBINA:    "98192",
			ChannelBNI:     "8428",
		},
		BusinessHandler: &MockBusinessHandler{},
	}
}

func TestNewService(t *testing.T) {
	service := NewService()
	if service == nil {
		t.Fatal("NewService() returned nil")
	}
}

func TestService_Setup(t *testing.T) {
	service := NewService()
	config := createTestConfig()

	setupService := service.Setup(config)
	if setupService == nil {
		t.Fatal("Setup() returned nil")
	}
}

func TestService_GenerateToken(t *testing.T) {
	service := NewService().Setup(createTestConfig())
	ctx := context.Background()

	tests := []struct {
		name    string
		req     TokenRequest
		wantErr bool
	}{
		{
			name: "valid credentials",
			req: TokenRequest{
				Username: "testuser",
				Password: "testpass",
			},
			wantErr: false,
		},
		{
			name: "empty username",
			req: TokenRequest{
				Username: "",
				Password: "testpass",
			},
			wantErr: true,
		},
		{
			name: "empty password",
			req: TokenRequest{
				Username: "testuser",
				Password: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.GenerateToken(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if resp.Meta.Code != 200 {
					t.Errorf("GenerateToken() response code = %d, want 200", resp.Meta.Code)
				}
				if resp.Data.IDToken == "" {
					t.Error("GenerateToken() token is empty")
				}
				if resp.Data.Username != tt.req.Username {
					t.Errorf("GenerateToken() username = %s, want %s", resp.Data.Username, tt.req.Username)
				}
			}
		})
	}
}

func TestService_ValidateToken(t *testing.T) {
	service := NewService().Setup(createTestConfig())
	ctx := context.Background()

	// Generate a valid token first
	tokenReq := TokenRequest{Username: "testuser", Password: "testpass"}
	tokenResp, err := service.GenerateToken(ctx, tokenReq)
	if err != nil {
		t.Fatalf("Failed to generate token for test: %v", err)
	}

	tests := []struct {
		name    string
		token   string
		wantErr bool
	}{
		{
			name:    "valid token",
			token:   tokenResp.Data.IDToken,
			wantErr: false,
		},
		{
			name:    "invalid token",
			token:   "invalid.token.here",
			wantErr: true,
		},
		{
			name:    "empty token",
			token:   "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := service.ValidateToken(ctx, tt.token)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if claims.Username != "testuser" {
					t.Errorf("ValidateToken() username = %s, want testuser", claims.Username)
				}
				if claims.AppName != "OTTOPAY" {
					t.Errorf("ValidateToken() app_name = %s, want OTTOPAY", claims.AppName)
				}
			}
		})
	}
}

func TestService_ProcessInquiry(t *testing.T) {
	service := NewService().Setup(createTestConfig())
	ctx := context.Background()

	tests := []struct {
		name       string
		req        InquiryRequest
		wantErr    bool
		wantStatus string
	}{
		{
			name: "valid inquiry",
			req: InquiryRequest{
				CompanyCode:    "71101",
				CustomerNumber: "***********",
				RequestID:      "REQ123456789",
				ChannelType:    ChannelMandiri,
			},
			wantErr:    false,
			wantStatus: InquiryStatusSuccess,
		},
		{
			name: "empty company code",
			req: InquiryRequest{
				CompanyCode:    "",
				CustomerNumber: "***********",
				RequestID:      "REQ123456789",
				ChannelType:    ChannelMandiri,
			},
			wantErr:    false,
			wantStatus: InquiryStatusFailed,
		},
		{
			name: "empty customer number",
			req: InquiryRequest{
				CompanyCode:    "71101",
				CustomerNumber: "",
				RequestID:      "REQ123456789",
				ChannelType:    ChannelMandiri,
			},
			wantErr:    false,
			wantStatus: InquiryStatusFailed,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.ProcessInquiry(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessInquiry() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if resp.InquiryStatus != tt.wantStatus {
				t.Errorf("ProcessInquiry() status = %s, want %s", resp.InquiryStatus, tt.wantStatus)
			}

			if tt.wantStatus == InquiryStatusSuccess {
				if resp.CustomerName == "" {
					t.Error("ProcessInquiry() customer name is empty")
				}
				if resp.TotalAmount == "" {
					t.Error("ProcessInquiry() total amount is empty")
				}
			}
		})
	}
}

func TestService_ProcessPayment(t *testing.T) {
	service := NewService().Setup(createTestConfig())
	ctx := context.Background()

	tests := []struct {
		name       string
		req        PaymentRequest
		wantErr    bool
		wantStatus string
	}{
		{
			name: "valid payment",
			req: PaymentRequest{
				CompanyCode:    "71101",
				CustomerNumber: "***********",
				RequestID:      "PAY123456789",
				ChannelType:    ChannelMandiri,
				CustomerName:   "Test Customer",
				CurrencyCode:   CurrencyIDR,
				PaidAmount:     "150000.00",
				TotalAmount:    "150000.00",
				Reference:      "REF123456789",
			},
			wantErr:    false,
			wantStatus: PaymentStatusSuccess,
		},
		{
			name: "empty customer name",
			req: PaymentRequest{
				CompanyCode:    "71101",
				CustomerNumber: "***********",
				RequestID:      "PAY123456789",
				ChannelType:    ChannelMandiri,
				CustomerName:   "",
				CurrencyCode:   CurrencyIDR,
				PaidAmount:     "150000.00",
				TotalAmount:    "150000.00",
				Reference:      "REF123456789",
			},
			wantErr:    false,
			wantStatus: PaymentStatusFailed,
		},
		{
			name: "invalid currency",
			req: PaymentRequest{
				CompanyCode:    "71101",
				CustomerNumber: "***********",
				RequestID:      "PAY123456789",
				ChannelType:    ChannelMandiri,
				CustomerName:   "Test Customer",
				CurrencyCode:   "EUR",
				PaidAmount:     "150000.00",
				TotalAmount:    "150000.00",
				Reference:      "REF123456789",
			},
			wantErr:    false,
			wantStatus: PaymentStatusFailed,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.ProcessPayment(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if resp.PaymentFlagStatus != tt.wantStatus {
				t.Errorf("ProcessPayment() status = %s, want %s", resp.PaymentFlagStatus, tt.wantStatus)
			}

			if tt.wantStatus == PaymentStatusSuccess {
				if resp.CustomerName == "" {
					t.Error("ProcessPayment() customer name is empty")
				}
				if resp.PaidAmount == "" {
					t.Error("ProcessPayment() paid amount is empty")
				}
			}
		})
	}
}

func TestService_ProcessInquiryWithMockFailure(t *testing.T) {
	mockHandler := &MockBusinessHandler{
		handleInquiryFunc: func(ctx context.Context, req InquiryRequest) (InquiryData, error) {
			return InquiryData{
				IsValid:      false,
				ErrorMessage: "Customer not found",
			}, nil
		},
	}

	config := createTestConfig()
	config.BusinessHandler = mockHandler
	service := NewService().Setup(config)
	ctx := context.Background()

	req := InquiryRequest{
		CompanyCode:    "71101",
		CustomerNumber: "***********",
		RequestID:      "REQ123456789",
		ChannelType:    ChannelMandiri,
	}

	resp, err := service.ProcessInquiry(ctx, req)
	if err != nil {
		t.Errorf("ProcessInquiry() unexpected error = %v", err)
	}

	if resp.InquiryStatus != InquiryStatusFailed {
		t.Errorf("ProcessInquiry() status = %s, want %s", resp.InquiryStatus, InquiryStatusFailed)
	}
}

func TestService_ProcessPaymentWithMockFailure(t *testing.T) {
	mockHandler := &MockBusinessHandler{
		handlePaymentFunc: func(ctx context.Context, req PaymentRequest) (PaymentData, error) {
			return PaymentData{
				IsSuccess:    false,
				ErrorMessage: "Insufficient balance",
			}, nil
		},
	}

	config := createTestConfig()
	config.BusinessHandler = mockHandler
	service := NewService().Setup(config)
	ctx := context.Background()

	req := PaymentRequest{
		CompanyCode:    "71101",
		CustomerNumber: "***********",
		RequestID:      "PAY123456789",
		ChannelType:    ChannelMandiri,
		CustomerName:   "Test Customer",
		CurrencyCode:   CurrencyIDR,
		PaidAmount:     "150000.00",
		TotalAmount:    "150000.00",
		Reference:      "REF123456789",
	}

	resp, err := service.ProcessPayment(ctx, req)
	if err != nil {
		t.Errorf("ProcessPayment() unexpected error = %v", err)
	}

	if resp.PaymentFlagStatus != PaymentStatusFailed {
		t.Errorf("ProcessPayment() status = %s, want %s", resp.PaymentFlagStatus, PaymentStatusFailed)
	}
}
