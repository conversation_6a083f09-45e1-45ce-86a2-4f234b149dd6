package ottopay

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
)

// SecurityMiddleware provides additional security features
type SecurityMiddleware struct {
	config Config
}

// NewSecurityMiddleware creates a new security middleware instance
func NewSecurityMiddleware(config Config) *SecurityMiddleware {
	return &SecurityMiddleware{config: config}
}

// IPWhitelistMiddleware validates client IP against whitelist
func (sm *SecurityMiddleware) IPWhitelistMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !sm.config.EnableIPAuth {
			return c.Next()
		}

		clientIP := c.IP()
		if !sm.config.IsIPAllowed(clientIP) {
			return c.Status(fiber.StatusForbidden).JSON(NewErrorResponse(403, "IP address not allowed"))
		}

		return c.Next()
	}
}

// SignatureValidationMiddleware validates request signatures
func (sm *SecurityMiddleware) SignatureValidationMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !sm.config.EnableSignatureValidation {
			return c.Next()
		}

		// Skip signature validation for token endpoint
		if c.Path() == "/token" || c.Path() == "/ottopay/token" {
			return c.Next()
		}

		signature := c.Get("X-Signature")
		if signature == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(401, "Signature required"))
		}

		// Get request body for signature validation
		body := c.Body()
		if len(body) == 0 {
			return c.Status(fiber.StatusBadRequest).JSON(NewErrorResponse(400, "Request body required for signature validation"))
		}

		// Validate signature
		if !sm.validateSignature(string(body), signature) {
			return c.Status(fiber.StatusUnauthorized).JSON(NewErrorResponse(401, "Invalid signature"))
		}

		return c.Next()
	}
}

// RateLimitMiddleware provides basic rate limiting
func (sm *SecurityMiddleware) RateLimitMiddleware(maxRequests int, window time.Duration) fiber.Handler {
	// Simple in-memory rate limiter (for production, use Redis or similar)
	requestCounts := make(map[string][]time.Time)

	return func(c *fiber.Ctx) error {
		clientIP := c.IP()
		now := time.Now()

		// Clean old entries
		if requests, exists := requestCounts[clientIP]; exists {
			var validRequests []time.Time
			for _, reqTime := range requests {
				if now.Sub(reqTime) < window {
					validRequests = append(validRequests, reqTime)
				}
			}
			requestCounts[clientIP] = validRequests
		}

		// Check rate limit
		if len(requestCounts[clientIP]) >= maxRequests {
			return c.Status(fiber.StatusTooManyRequests).JSON(NewErrorResponse(429, "Rate limit exceeded"))
		}

		// Add current request
		requestCounts[clientIP] = append(requestCounts[clientIP], now)

		return c.Next()
	}
}

// RequestLoggingMiddleware logs requests for audit purposes
func (sm *SecurityMiddleware) RequestLoggingMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()

		// Process request
		err := c.Next()

		// Log request details
		duration := time.Since(start)
		userID := ""
		username := ""

		if uid := c.Locals("user_id"); uid != nil {
			if id, ok := uid.(string); ok {
				userID = id
			}
		}
		if uname := c.Locals("username"); uname != nil {
			if name, ok := uname.(string); ok {
				username = name
			}
		}

		// Log format: timestamp, method, path, status, duration, user_id, username, ip
		logEntry := fmt.Sprintf("[%s] %s %s - %d - %v - User: %s (%s) - IP: %s",
			time.Now().Format(time.RFC3339),
			c.Method(),
			c.Path(),
			c.Response().StatusCode(),
			duration,
			username,
			userID,
			c.IP(),
		)

		// In production, use proper logging library
		fmt.Println(logEntry)

		return err
	}
}

// HeaderSecurityMiddleware adds security headers
func (sm *SecurityMiddleware) HeaderSecurityMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Add security headers
		c.Set("X-Content-Type-Options", "nosniff")
		c.Set("X-Frame-Options", "DENY")
		c.Set("X-XSS-Protection", "1; mode=block")
		c.Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.Set("Content-Security-Policy", "default-src 'self'")

		return c.Next()
	}
}

// validateSignature validates HMAC signature
func (sm *SecurityMiddleware) validateSignature(payload, signature string) bool {
	if sm.config.SignatureSecret == "" {
		return false
	}

	// Calculate expected signature
	mac := hmac.New(sha256.New, []byte(sm.config.SignatureSecret))
	mac.Write([]byte(payload))
	expectedSignature := hex.EncodeToString(mac.Sum(nil))

	// Compare signatures (constant time comparison)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// CORSMiddleware provides CORS configuration for OttoPay
func CORSMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Allow specific origins or all origins for development
		// In production, configure specific allowed origins
		c.Set("Access-Control-Allow-Origin", "*")
		c.Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Signature, X-API-Key")
		c.Set("Access-Control-Max-Age", "86400")

		// Handle preflight requests
		if c.Method() == "OPTIONS" {
			return c.SendStatus(fiber.StatusNoContent)
		}

		return c.Next()
	}
}

// TimeoutMiddleware provides request timeout handling
func TimeoutMiddleware(timeout time.Duration) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Create timeout channel
		done := make(chan bool, 1)

		go func() {
			c.Next()
			done <- true
		}()

		select {
		case <-done:
			return nil
		case <-time.After(timeout):
			return c.Status(fiber.StatusRequestTimeout).JSON(NewErrorResponse(408, "Request timeout"))
		}
	}
}

// ValidationMiddleware provides request validation
func ValidationMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Validate content type for POST requests
		if c.Method() == "POST" {
			contentType := c.Get("Content-Type")
			if !strings.Contains(contentType, "application/json") {
				return c.Status(fiber.StatusUnsupportedMediaType).JSON(
					NewErrorResponse(415, "Content-Type must be application/json"))
			}
		}

		// Validate request size (max 1MB)
		if len(c.Body()) > 1024*1024 {
			return c.Status(fiber.StatusRequestEntityTooLarge).JSON(
				NewErrorResponse(413, "Request body too large"))
		}

		return c.Next()
	}
}

// HealthCheckMiddleware provides health check functionality
func HealthCheckMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		if c.Path() == "/health" || c.Path() == "/ottopay/health" {
			return c.JSON(fiber.Map{
				"status":    "ok",
				"timestamp": time.Now().Unix(),
				"service":   "ottopay",
				"version":   "1.0.0",
			})
		}
		return c.Next()
	}
}

// ErrorHandlingMiddleware provides centralized error handling
func ErrorHandlingMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		err := c.Next()

		if err != nil {
			// Log error
			fmt.Printf("Error: %v\n", err)

			// Handle different error types
			if e, ok := err.(*fiber.Error); ok {
				return c.Status(e.Code).JSON(NewErrorResponse(e.Code, e.Message))
			}

			// Default error response
			return c.Status(fiber.StatusInternalServerError).JSON(
				NewErrorResponse(500, "Internal server error"))
		}

		return nil
	}
}
