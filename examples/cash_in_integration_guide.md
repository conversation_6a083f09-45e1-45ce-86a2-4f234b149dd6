# OttoPay Integration with Cash-In Transaction Module

This guide shows how to integrate the OttoPay package with the existing `cash_in_transaction` module in the payment service.

## Overview

The integration follows the same patterns as existing payment providers (Xendit, NicePay, etc.) and integrates with the cash-in transaction flow. Based on the analysis of `trx_process.go` and `handler_cashin.go`, OttoPay will be added as a new provider in the transaction processing switch statement.

## Key Integration Points

Based on the analysis of the existing codebase:

1. **Add OttoPay to constants** (`utils/constants/payment_provider.go`)
2. **Add OttoPay infrastructure** (similar to `infrastructure/xendit`, `infrastructure/nicepay`)
3. **Update transaction processing** (`modules/cash_in_transaction/usecase/trx_process.go`)
4. **Add callback handling** (`modules/cash_in_transaction/handler/handler_callback.go`)
5. **Update configuration** (`config.json`)

## Integration Steps

### 1. Add OttoPay Constants

First, add OttoPay to the payment provider constants:

```go
// utils/constants/payment_provider.go
const (
    ProviderXfers       = "xfers"
    ProviderXendit      = "xendit"
    ProviderNicePay     = "nicepay"
    ProviderOttocash    = "ottocash"
    ProviderDoku        = "doku"
    ProviderCashlez     = "cashlez"
    ProviderBankBCA     = "bank bca"
    ProviderSnapNicePay = "snap nicepay"
    ProviderOttoPay     = "ottopay"  // Add this line
)
```

Add OttoPay status constants:

```go
// utils/constants/payment_status.go
// OttoPay status constants
const (
    OttoPayStatusSuccess = "00"
    OttoPayStatusFailed  = "01"
    OttoPayStatusTimeout = "02"
)
```

### 2. Database Setup

First, add OttoPay as a payment provider in your database:

```sql
-- Add OttoPay as a payment provider
INSERT INTO payment_providers (name, code, is_active, created_at, updated_at) 
VALUES ('OttoPay', 'OTTOPAY', true, NOW(), NOW());

-- Add OttoPay channels for each bank
INSERT INTO payment_channels (provider_id, name, code, channel_type, is_active, created_at, updated_at)
VALUES 
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay Mandiri', 'OTTOPAY_MANDIRI', 'VIRTUAL_ACCOUNT', true, NOW(), NOW()),
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay BRI', 'OTTOPAY_BRI', 'VIRTUAL_ACCOUNT', true, NOW(), NOW()),
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay BINA', 'OTTOPAY_BINA', 'VIRTUAL_ACCOUNT', true, NOW(), NOW()),
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay BNI', 'OTTOPAY_BNI', 'VIRTUAL_ACCOUNT', true, NOW(), NOW());

-- Add company payment provider channel mappings
INSERT INTO company_payment_provider_channel_mappings (company_id, payment_provider_id, payment_channel_id, is_active, created_at, updated_at)
VALUES 
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_MANDIRI'), true, NOW(), NOW()),
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_BRI'), true, NOW(), NOW()),
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_BINA'), true, NOW(), NOW()),
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_BNI'), true, NOW(), NOW());
```

### 3. Update Transaction Processing

Add OttoPay to the transaction processing logic in `trx_process.go`:

```go
// modules/cash_in_transaction/usecase/trx_process.go

// Add OttoPay import
import (
    // ... existing imports
    "repo.nusatek.id/nusatek/ottopay"
)

// Add OttoPay to the provider switch statement in ProcessCashinTransaction
func (u *cashinTransactionUsecase) ProcessCashinTransaction(ctx context.Context, req entity.CashinTransactionReq) (entity.CashinTransactionRes, error) {
    // ... existing code ...

    switch paymentProvider.Name {
    case constants.ProviderXfers:
        // ... existing xfers code
    case constants.ProviderXendit:
        // ... existing xendit code
    case constants.ProviderNicePay:
        // ... existing nicepay code
    case constants.ProviderOttocash:
        // ... existing ottocash code
    case constants.ProviderOttoPay:  // Add this case
        return u.processOttoPayTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
    default:
        return entity.CashinTransactionRes{}, errors.New("payment provider not supported")
    }
}

// Add OttoPay processing method
func (u *cashinTransactionUsecase) processOttoPayTransaction(
    ctx context.Context,
    req entity.CashinTransactionReq,
    paymentProvider *domain.PaymentProviders,
    paymentChannel *domain.PaymentChannels,
    mapping *domain.CompanyPaymentProviderChannelMappings,
) (entity.CashinTransactionRes, error) {

    // Generate VA number based on channel type
    var channelType string
    switch paymentChannel.Code {
    case "OTTOPAY_MANDIRI":
        channelType = ottopay.ChannelMandiri
    case "OTTOPAY_BRI":
        channelType = ottopay.ChannelBRI
    case "OTTOPAY_BINA":
        channelType = ottopay.ChannelBINA
    case "OTTOPAY_BNI":
        channelType = ottopay.ChannelBNI
    default:
        return entity.CashinTransactionRes{}, errors.New("unsupported OttoPay channel")
    }

    // Generate VA number
    prefix := ottopay.BankPrefixes[channelType]
    customerNumber := req.CustomerPhone
    if len(customerNumber) > 11 {
        customerNumber = customerNumber[len(customerNumber)-11:]
    }
    vaNumber := prefix + customerNumber

    // Create cash_in_transaction record
    cashInTransaction := &domain.CashInTransactions{
        CompanyID:                req.CompanyID,
        CompanyProductID:         req.CompanyProductID,
        InvoiceNumber:           u.generateInvoiceNumber(),
        CustomerName:            req.CustomerName,
        CustomerPhone:           req.CustomerPhone,
        CustomerEmail:           req.CustomerEmail,
        PaymentProviderID:       paymentProvider.ID,
        PaymentChannelID:        paymentChannel.ID,
        Total:                   req.Total,
        AdminFee:               req.AdminFee,
        Discount:               req.Discount,
        Voucher:                req.Voucher,
        PgDeliveryFee:          req.PgDeliveryFee,
        PaymentStatus:          constants.PaymentPending,
        Status:                 constants.CashInStatusPending,
        VirtualAccount:         vaNumber,
        ExpiredAt:              &req.ExpiredAt,
        CreatedAt:              time.Now(),
        UpdatedAt:              time.Now(),
    }

    // Save to database
    err := u.cashinRepo.CreateCashinTransaction(ctx, u.db, cashInTransaction)
    if err != nil {
        return entity.CashinTransactionRes{}, err
    }

    // Create transaction items
    for _, item := range req.Items {
        cashInItem := &domain.CashInTransactionItems{
            CashInTransactionID: cashInTransaction.ID,
            ProductName:         item.ProductName,
            ProductCode:         item.ProductCode,
            Quantity:           item.Quantity,
            Price:              item.Price,
            Status:             constants.CashInItemStatusPending,
            CreatedAt:          time.Now(),
            UpdatedAt:          time.Now(),
        }

        err = u.cashinRepo.CreateCashinTransactionItem(ctx, u.db, cashInItem)
        if err != nil {
            return entity.CashinTransactionRes{}, err
        }
    }

    // Create transaction history
    history := &domain.CashInTransactionHistories{
        CashInTransactionID:    cashInTransaction.ID,
        Description:           "OttoPay transaction created",
        PaymentStatus:         constants.PaymentPending,
        Status:               constants.CashInStatusPending,
        VirtualAccount:       vaNumber,
        ProviderId:           paymentProvider.ID,
        ChannelId:            paymentChannel.ID,
        PgReferenceInformation: fmt.Sprintf(`{"va_number":"%s","channel_type":"%s"}`, vaNumber, channelType),
        CreatedAt:            time.Now(),
        UpdatedAt:            time.Now(),
    }

    err = u.cashinRepo.CreateCashinTransactionHistory(ctx, u.db, history)
    if err != nil {
        return entity.CashinTransactionRes{}, err
    }

    // Return response
    return entity.CashinTransactionRes{
        ID:                    cashInTransaction.ID,
        InvoiceNumber:        cashInTransaction.InvoiceNumber,
        VirtualAccount:       vaNumber,
        Total:               cashInTransaction.Total,
        AdminFee:            cashInTransaction.AdminFee,
        PgDeliveryFee:       cashInTransaction.PgDeliveryFee,
        GrandTotal:          cashInTransaction.Total + cashInTransaction.AdminFee + cashInTransaction.PgDeliveryFee,
        ExpiredAt:           cashInTransaction.ExpiredAt,
        PaymentProviderName: paymentProvider.Name,
        PaymentChannelName:  paymentChannel.Name,
        Status:              cashInTransaction.Status,
        PaymentStatus:       cashInTransaction.PaymentStatus,
    }, nil
}
```

### 4. Database Integration Adapter

Create an adapter that implements the OttoPay `DatabaseIntegration` interface:

```go
// DatabaseIntegrationAdapter adapts existing cash_in_transaction repository to OttoPay
type DatabaseIntegrationAdapter struct {
    cashinRepo usecase.CashinTranscationRepo
    db         *gorm.DB
}

func (d *DatabaseIntegrationAdapter) GetVirtualAccount(ctx context.Context, vaNumber string) (*ottopay.VirtualAccount, error) {
    // Extract customer number from VA number
    // VA format: [Prefix][CustomerNumber]
    // Example: 71101*********** -> prefix: 71101, customer: ***********
    
    prefix, customerNumber := d.extractPrefixAndCustomer(vaNumber)
    
    // Query cash_in_transactions by customer phone or invoice number
    cashIn, err := d.cashinRepo.GetCashinTransactionByCustomerPhone(ctx, customerNumber)
    if err != nil {
        // Try by invoice number if phone lookup fails
        cashIn, err = d.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, customerNumber)
        if err != nil {
            return nil, fmt.Errorf("virtual account not found")
        }
    }
    
    // Convert CashInTransaction to VirtualAccount
    va := &ottopay.VirtualAccount{
        ID:             fmt.Sprintf("va-%d", cashIn.ID),
        VANumber:       vaNumber,
        CompanyCode:    prefix,
        CustomerNumber: customerNumber,
        ChannelType:    d.getChannelTypeFromPrefix(prefix),
        CustomerName:   cashIn.CustomerName,
        Amount:         cashIn.Total + cashIn.AdminFee + cashIn.PgDeliveryFee,
        Status:         d.mapCashInStatusToVAStatus(cashIn.Status, cashIn.PaymentStatus),
        CreatedAt:      cashIn.CreatedAt,
        UpdatedAt:      cashIn.UpdatedAt,
        ExpiresAt:      *cashIn.ExpiredAt,
    }
    
    return va, nil
}

func (d *DatabaseIntegrationAdapter) UpdateVirtualAccountStatus(ctx context.Context, vaNumber, status string) error {
    // Find the cash_in_transaction
    va, err := d.GetVirtualAccount(ctx, vaNumber)
    if err != nil {
        return err
    }
    
    cashInID, _ := strconv.Atoi(strings.TrimPrefix(va.ID, "va-"))
    
    // Map OttoPay status to internal status
    var cashInStatus, paymentStatus string
    switch status {
    case "PAID":
        cashInStatus = constants.CashInStatusPaid
        paymentStatus = constants.PaymentSuccess
    case "FAILED":
        cashInStatus = constants.CashInStatusFailed
        paymentStatus = constants.PaymentFailed
    case "EXPIRED":
        cashInStatus = constants.CashInStatusExpired
        paymentStatus = constants.PaymentExpired
    default:
        cashInStatus = constants.CashInStatusPending
        paymentStatus = constants.PaymentPending
    }
    
    // Update transaction in database
    tx := d.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    err = d.cashinRepo.UpdateStatusCashInTransaction(ctx, tx, cashInID, cashInStatus)
    if err != nil {
        tx.Rollback()
        return err
    }
    
    // Create transaction history
    history := &domain.CashInTransactionHistories{
        CashInTransactionID:    cashInID,
        Description:            fmt.Sprintf("OttoPay callback - Status updated to %s", status),
        PaymentStatus:          paymentStatus,
        Status:                 cashInStatus,
        VirtualAccount:         vaNumber,
        ProviderId:             d.getOttoPayProviderID(),
        ChannelId:              d.getChannelIDFromVA(vaNumber),
        PgReferenceInformation: fmt.Sprintf(`{"va_number":"%s","status":"%s","updated_at":"%s"}`, vaNumber, status, time.Now().Format(time.RFC3339)),
    }
    
    err = d.cashinRepo.CreateCashinTransactionHistory(ctx, tx, history)
    if err != nil {
        tx.Rollback()
        return err
    }
    
    return tx.Commit().Error
}
```

### 3. Business Logic Integration

Create a custom business handler that integrates with your cash-in logic:

```go
type CashInBusinessHandler struct {
    dbAdapter *DatabaseIntegrationAdapter
    validator *ottopay.Validator
}

func (h *CashInBusinessHandler) HandleInquiry(ctx context.Context, req ottopay.InquiryRequest) (ottopay.InquiryData, error) {
    // Validate request
    if err := h.validator.ValidateInquiryRequest(req); err != nil {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: err.Error()}, nil
    }
    
    // Generate VA number from company code + customer number
    vaNumber := req.CompanyCode + req.CustomerNumber
    
    // Get virtual account from database
    va, err := h.dbAdapter.GetVirtualAccount(ctx, vaNumber)
    if err != nil {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: "Virtual Account not found"}, nil
    }
    
    // Check if VA is active and not expired
    if va.Status != "ACTIVE" {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: "Virtual Account is not active"}, nil
    }
    
    if time.Now().After(va.ExpiresAt) {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: "Virtual Account has expired"}, nil
    }
    
    return ottopay.InquiryData{
        CustomerName:   va.CustomerName,
        TotalAmount:    va.Amount,
        CurrencyCode:   ottopay.CurrencyIDR,
        SubCompany:     ottopay.DefaultSubCompany,
        AdditionalData: "",
        IsValid:        true,
    }, nil
}

func (h *CashInBusinessHandler) HandlePayment(ctx context.Context, req ottopay.PaymentRequest) (ottopay.PaymentData, error) {
    // Validate request
    if err := h.validator.ValidatePaymentRequest(req); err != nil {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: err.Error()}, nil
    }
    
    // Parse amounts
    paidAmount, _ := strconv.ParseFloat(req.PaidAmount, 64)
    totalAmount, _ := strconv.ParseFloat(req.TotalAmount, 64)
    
    // Generate VA number
    vaNumber := req.CompanyCode + req.CustomerNumber
    
    // Get virtual account
    va, err := h.dbAdapter.GetVirtualAccount(ctx, vaNumber)
    if err != nil {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Virtual Account not found"}, nil
    }
    
    // Validate payment amount
    if paidAmount != va.Amount {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Payment amount does not match bill amount"}, nil
    }
    
    // Check for duplicate payment
    if va.Status == "PAID" {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Payment already processed"}, nil
    }
    
    // Process payment
    err = h.dbAdapter.UpdateVirtualAccountStatus(ctx, vaNumber, "PAID")
    if err != nil {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Payment processing failed"}, nil
    }
    
    return ottopay.PaymentData{
        IsSuccess:      true,
        CustomerName:   va.CustomerName,
        TotalAmount:    totalAmount,
        PaidAmount:     paidAmount,
        CurrencyCode:   req.CurrencyCode,
        AdditionalData: "",
    }, nil
}
```

### 4. Route Setup

Add OttoPay routes to your existing Fiber application:

```go
func SetupOttoPayRoutes(app *fiber.App, cashinRepo usecase.CashinTranscationRepo, db *gorm.DB) {
    // Create database adapter
    dbAdapter := NewDatabaseIntegrationAdapter(cashinRepo, db)
    
    // Create OttoPay configuration
    config := ottopay.NewConfigBuilder().
        WithJWTSecret(os.Getenv("OTTOPAY_JWT_SECRET")).
        WithTokenDuration(24 * time.Hour).
        WithAllowedIPs([]string{"************", "*************"}).
        WithIPAuth(true).
        WithRequestTimeout(3 * time.Second).
        Build()
    
    // Create business handler
    businessHandler := &CashInBusinessHandler{
        dbAdapter: dbAdapter,
        validator: ottopay.NewValidator(),
    }
    config.BusinessHandler = businessHandler
    
    // Create OttoPay service
    ottoPayService := ottopay.NewService().Setup(config)
    
    // Setup OttoPay routes under /ottopay prefix
    ottopay.SetupRoutes(app, ottoPayService, config)
    
    log.Println("OttoPay routes setup complete")
}
```

### 6. Add Callback Handling

Add OttoPay callback handling to `handler_callback.go`:

```go
// modules/cash_in_transaction/handler/handler_callback.go

// Add OttoPay callback route
func (h *HandlerCallback) CallbackOttoPay(c *fiber.Ctx) error {
    ctx := c.UserContext()

    // Get authorization token
    token := strings.TrimPrefix(c.Get("Authorization"), "Bearer ")
    if token == "" {
        return fiber.NewError(fiber.StatusUnauthorized, "Missing authorization token")
    }

    // Parse request body
    var req interface{}
    err := c.BodyParser(&req)
    if err != nil {
        return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
    }

    // Process callback
    err = h.PushCallbackOttoPayCashin(ctx, token, req, constants.TrxVirtualAccount)
    if err != nil {
        logutil.LogError(ctx, "OttoPay callback error", err)
        return fiber.NewError(fiber.StatusInternalServerError, "Callback processing failed")
    }

    return c.JSON(fiber.Map{"status": "ok"})
}

// Add OttoPay callback processing
func (h *HandlerCallback) PushCallbackOttoPayCashin(ctx context.Context, token string, req interface{}, paymentType string) error {
    switch paymentType {
    case constants.TrxVirtualAccount:
        // Handle VA callback
        var callback ottopay.PaymentResponse
        reqByte, _ := json.Marshal(req)
        err := json.Unmarshal(reqByte, &callback)
        if err != nil {
            return fmt.Errorf("failed to unmarshal OttoPay callback: %w", err)
        }

        // Validate token using OttoPay service
        _, err = h.ottoPayService.ValidateToken(ctx, token)
        if err != nil {
            return fmt.Errorf("invalid OttoPay token: %w", err)
        }

        // Process the callback asynchronously
        go h.CallbackOttoPayCashinTransactionProcess(ctx, callback.RequestID, callback.CompanyCode+callback.CustomerNumber, paymentType, reqByte)

    default:
        return fmt.Errorf("unsupported OttoPay payment type: %s", paymentType)
    }

    return nil
}

// Add OttoPay transaction processing
func (h *HandlerCallback) CallbackOttoPayCashinTransactionProcess(ctx context.Context, referenceId, vaNumber, paymentType string, body []byte) {
    // Get the cash_in_transaction by VA number
    cashIn, err := h.cashinRepo.GetCashinTransactionByVirtualAccount(ctx, vaNumber)
    if err != nil {
        logutil.LogError(ctx, fmt.Sprintf("OttoPay callback: failed to get cash_in_transaction for VA %s", vaNumber), err)
        return
    }

    // Process based on payment type
    switch paymentType {
    case constants.TrxVirtualAccount:
        // Parse the callback response
        var response ottopay.PaymentResponse
        err := json.Unmarshal(body, &response)
        if err != nil {
            logutil.LogError(ctx, "OttoPay callback: failed to unmarshal payment response", err)
            return
        }

        // Update transaction status based on OttoPay response
        err = h.updateStatusOttoPayVACashinTransaction(ctx, response, cashIn)
        if err != nil {
            logutil.LogError(ctx, "OttoPay callback: failed to update transaction status", err)
            return
        }

    default:
        logutil.LogError(ctx, fmt.Sprintf("OttoPay callback: unsupported payment type %s", paymentType), nil)
    }
}

// Update transaction status from OttoPay callback
func (h *HandlerCallback) updateStatusOttoPayVACashinTransaction(ctx context.Context, response ottopay.PaymentResponse, cashIn *domain.CashInTransactions) error {
    tx := h.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()

    var newStatus, paymentStatus string
    var paymentAt *time.Time

    // Map OttoPay status to internal status
    switch response.PaymentFlagStatus {
    case constants.OttoPayStatusSuccess:
        newStatus = constants.CashInStatusPaid
        paymentStatus = constants.PaymentPaid
        now := time.Now()
        paymentAt = &now

    case constants.OttoPayStatusFailed:
        newStatus = constants.CashInStatusFailed
        paymentStatus = constants.PaymentFailed

    case constants.OttoPayStatusTimeout:
        newStatus = constants.CashInStatusExpired
        paymentStatus = constants.PaymentExpired

    default:
        newStatus = constants.CashInStatusPending
        paymentStatus = constants.PaymentPending
    }

    // Update cash_in_transaction
    cashIn.Status = newStatus
    cashIn.PaymentStatus = paymentStatus
    if paymentAt != nil {
        cashIn.PaymentAt = int(paymentAt.Unix())
    }
    cashIn.UpdatedAt = time.Now()

    err := h.cashinRepo.UpdateCashinTransaction(ctx, tx, cashIn)
    if err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to update cash_in_transaction: %w", err)
    }

    // Create transaction history
    history := &domain.CashInTransactionHistories{
        CashInTransactionID:    cashIn.ID,
        Description:            fmt.Sprintf("OttoPay VA callback - %s", response.PaymentFlagReason.English),
        PaymentStatus:          paymentStatus,
        Status:                 newStatus,
        VirtualAccount:         response.CompanyCode + response.CustomerNumber,
        ProviderId:             cashIn.PaymentProviderID,
        ChannelId:              cashIn.PaymentChannelID,
        PgReferenceInformation: string(body),
        CreatedAt:              time.Now(),
        UpdatedAt:              time.Now(),
    }

    err = h.cashinRepo.CreateCashinTransactionHistory(ctx, tx, history)
    if err != nil {
        tx.Rollback()
        return fmt.Errorf("failed to create transaction history: %w", err)
    }

    // Update transaction items if paid
    if newStatus == constants.CashInStatusPaid {
        err = h.cashinRepo.UpdateStatusCashinTransactionItemsByCashinTranscationId(ctx, cashIn.ID, constants.CashInItemStatusPaid)
        if err != nil {
            tx.Rollback()
            return fmt.Errorf("failed to update transaction items: %w", err)
        }
    }

    err = tx.Commit().Error
    if err != nil {
        return fmt.Errorf("failed to commit transaction: %w", err)
    }

    // Publish callback to client (similar to existing providers)
    if err := h.cashinUsecase.PublishCallbackToClient(ctx, cashIn.InvoiceNumber, nil, nil, nil); err != nil {
        logutil.LogError(ctx, "OttoPay callback: failed to publish callback to client", err)
    }

    return nil
}
```

### 7. Add Message Broker Constants

Add OttoPay callback constants to `utils/constants/constant.go`:

```go
// utils/constants/constant.go
const (
    // ... existing constants
    CallbackCashInOttoPayVa = "payment.service.callback.ottopay.virtual"
)
```

### 8. Callback Processing

Add callback processing similar to existing providers:

```go
// Add to your callback handler
func (h *HandlerCallback) CallbackOttoPay(c *fiber.Ctx) error {
    ctx := c.UserContext()
    
    // Get authorization token
    token := strings.TrimPrefix(c.Get("Authorization"), "Bearer ")
    if token == "" {
        return fiber.NewError(fiber.StatusUnauthorized, "Missing authorization token")
    }
    
    // Parse request body
    var req interface{}
    err := c.BodyParser(&req)
    if err != nil {
        return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
    }
    
    // Process callback
    err = h.PushCallbackOttoPayCashin(ctx, token, req, constants.TrxVirtualAccount)
    if err != nil {
        log.Printf("OttoPay callback error: %v", err)
        return fiber.NewError(fiber.StatusInternalServerError, "Callback processing failed")
    }
    
    return c.JSON(fiber.Map{"status": "ok"})
}

func (h *HandlerCallback) PushCallbackOttoPayCashin(ctx context.Context, token string, req interface{}, paymentType string) error {
    // Validate token and process callback
    // Similar to PushCallbackXenditCashin, PushCallbackXfersCashin, etc.
    
    switch paymentType {
    case constants.TrxVirtualAccount:
        // Handle VA callback
        var callback ottopay.PaymentResponse
        reqByte, _ := json.Marshal(req)
        err := json.Unmarshal(reqByte, &callback)
        if err != nil {
            return fmt.Errorf("failed to unmarshal OttoPay callback: %w", err)
        }
        
        // Process the callback asynchronously
        go h.CallbackOttoPayCashinTransactionProcess(ctx, callback.RequestID, callback.CompanyCode+callback.CustomerNumber, paymentType, reqByte)
        
    default:
        return fmt.Errorf("unsupported OttoPay payment type: %s", paymentType)
    }
    
    return nil
}
```

### 6. Environment Configuration

Add OttoPay configuration to your environment:

```bash
# OttoPay Configuration
OTTOPAY_JWT_SECRET=your-ottopay-jwt-secret-key
OTTOPAY_TOKEN_DURATION=24h
OTTOPAY_ENABLE_IP_AUTH=true
OTTOPAY_ALLOWED_IPS=************,*************
OTTOPAY_REQUEST_TIMEOUT=3s

# Bank Prefixes
OTTOPAY_PREFIX_MANDIRI=71101
OTTOPAY_PREFIX_BRI=15772
OTTOPAY_PREFIX_BINA=98192
OTTOPAY_PREFIX_BNI=8428
```

### 7. Virtual Account Generation

When creating cash-in transactions, generate VA numbers using OttoPay format:

```go
func GenerateOttoPayVA(channelType, customerPhone string) string {
    // Get prefix for the channel
    prefix := ottopay.BankPrefixes[channelType]
    
    // Use customer phone as customer number (max 11 digits)
    customerNumber := customerPhone
    if len(customerNumber) > 11 {
        customerNumber = customerNumber[len(customerNumber)-11:]
    }
    
    return prefix + customerNumber
}

// Example usage in cash-in creation
func CreateCashInWithOttoPay(req CreateCashInRequest) {
    // Create cash-in transaction
    cashIn := &domain.CashInTransactions{
        CustomerPhone: req.CustomerPhone,
        CustomerName:  req.CustomerName,
        Total:         req.Amount,
        // ... other fields
    }
    
    // Generate VA number for OttoPay
    if req.PaymentChannel == "OTTOPAY_MANDIRI" {
        vaNumber := GenerateOttoPayVA(ottopay.ChannelMandiri, req.CustomerPhone)
        cashIn.VirtualAccount = vaNumber
    }
    
    // Save to database
    // ...
}
```

## Testing

1. **Unit Tests**: Test the database adapter and business handler
2. **Integration Tests**: Test the complete flow with mock OttoPay callbacks
3. **Manual Testing**: Use the OttoPay simulator for end-to-end testing

## Monitoring

The OttoPay package includes built-in logging and monitoring. All transactions are logged with:
- Request/response details
- Performance metrics
- Error tracking
- Audit trails

## Security

- JWT token validation for all requests
- IP whitelisting for OttoPay servers
- Request signature validation (optional)
- Input validation and sanitization

This integration follows the same patterns as existing payment providers and seamlessly integrates with the cash-in transaction flow.
