# OttoPay Integration with Cash-In Transaction Module

This guide shows how to integrate the OttoPay package with the existing `cash_in_transaction` module in the payment service.

## Overview

The integration follows the same patterns as existing payment providers (Xendit, NicePay, etc.) and integrates with the cash-in transaction flow.

## Integration Steps

### 1. Database Setup

First, add <PERSON><PERSON>ay as a payment provider in your database:

```sql
-- Add OttoPay as a payment provider
INSERT INTO payment_providers (name, code, is_active, created_at, updated_at) 
VALUES ('OttoPay', 'OTTOPAY', true, NOW(), NOW());

-- Add OttoPay channels for each bank
INSERT INTO payment_channels (provider_id, name, code, channel_type, is_active, created_at, updated_at)
VALUES 
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay Mandiri', 'OTTOPAY_MANDIRI', 'VIRTUAL_ACCOUNT', true, NOW(), NOW()),
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay BRI', 'OTTOPAY_BRI', 'VIRTUAL_ACCOUNT', true, NOW(), NOW()),
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay BINA', 'OTTOPAY_BINA', 'VIRTUAL_ACCOUNT', true, NOW(), NOW()),
    ((SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), 'OttoPay BNI', 'OTTOPAY_BNI', 'VIRTUAL_ACCOUNT', true, NOW(), NOW());

-- Add company payment provider channel mappings
INSERT INTO company_payment_provider_channel_mappings (company_id, payment_provider_id, payment_channel_id, is_active, created_at, updated_at)
VALUES 
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_MANDIRI'), true, NOW(), NOW()),
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_BRI'), true, NOW(), NOW()),
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_BINA'), true, NOW(), NOW()),
    (1, (SELECT id FROM payment_providers WHERE code = 'OTTOPAY'), (SELECT id FROM payment_channels WHERE code = 'OTTOPAY_BNI'), true, NOW(), NOW());
```

### 2. Database Integration Adapter

Create an adapter that implements the OttoPay `DatabaseIntegration` interface:

```go
// DatabaseIntegrationAdapter adapts existing cash_in_transaction repository to OttoPay
type DatabaseIntegrationAdapter struct {
    cashinRepo usecase.CashinTranscationRepo
    db         *gorm.DB
}

func (d *DatabaseIntegrationAdapter) GetVirtualAccount(ctx context.Context, vaNumber string) (*ottopay.VirtualAccount, error) {
    // Extract customer number from VA number
    // VA format: [Prefix][CustomerNumber]
    // Example: **************** -> prefix: 71101, customer: ***********
    
    prefix, customerNumber := d.extractPrefixAndCustomer(vaNumber)
    
    // Query cash_in_transactions by customer phone or invoice number
    cashIn, err := d.cashinRepo.GetCashinTransactionByCustomerPhone(ctx, customerNumber)
    if err != nil {
        // Try by invoice number if phone lookup fails
        cashIn, err = d.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, customerNumber)
        if err != nil {
            return nil, fmt.Errorf("virtual account not found")
        }
    }
    
    // Convert CashInTransaction to VirtualAccount
    va := &ottopay.VirtualAccount{
        ID:             fmt.Sprintf("va-%d", cashIn.ID),
        VANumber:       vaNumber,
        CompanyCode:    prefix,
        CustomerNumber: customerNumber,
        ChannelType:    d.getChannelTypeFromPrefix(prefix),
        CustomerName:   cashIn.CustomerName,
        Amount:         cashIn.Total + cashIn.AdminFee + cashIn.PgDeliveryFee,
        Status:         d.mapCashInStatusToVAStatus(cashIn.Status, cashIn.PaymentStatus),
        CreatedAt:      cashIn.CreatedAt,
        UpdatedAt:      cashIn.UpdatedAt,
        ExpiresAt:      *cashIn.ExpiredAt,
    }
    
    return va, nil
}

func (d *DatabaseIntegrationAdapter) UpdateVirtualAccountStatus(ctx context.Context, vaNumber, status string) error {
    // Find the cash_in_transaction
    va, err := d.GetVirtualAccount(ctx, vaNumber)
    if err != nil {
        return err
    }
    
    cashInID, _ := strconv.Atoi(strings.TrimPrefix(va.ID, "va-"))
    
    // Map OttoPay status to internal status
    var cashInStatus, paymentStatus string
    switch status {
    case "PAID":
        cashInStatus = constants.CashInStatusPaid
        paymentStatus = constants.PaymentSuccess
    case "FAILED":
        cashInStatus = constants.CashInStatusFailed
        paymentStatus = constants.PaymentFailed
    case "EXPIRED":
        cashInStatus = constants.CashInStatusExpired
        paymentStatus = constants.PaymentExpired
    default:
        cashInStatus = constants.CashInStatusPending
        paymentStatus = constants.PaymentPending
    }
    
    // Update transaction in database
    tx := d.db.Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    err = d.cashinRepo.UpdateStatusCashInTransaction(ctx, tx, cashInID, cashInStatus)
    if err != nil {
        tx.Rollback()
        return err
    }
    
    // Create transaction history
    history := &domain.CashInTransactionHistories{
        CashInTransactionID:    cashInID,
        Description:            fmt.Sprintf("OttoPay callback - Status updated to %s", status),
        PaymentStatus:          paymentStatus,
        Status:                 cashInStatus,
        VirtualAccount:         vaNumber,
        ProviderId:             d.getOttoPayProviderID(),
        ChannelId:              d.getChannelIDFromVA(vaNumber),
        PgReferenceInformation: fmt.Sprintf(`{"va_number":"%s","status":"%s","updated_at":"%s"}`, vaNumber, status, time.Now().Format(time.RFC3339)),
    }
    
    err = d.cashinRepo.CreateCashinTransactionHistory(ctx, tx, history)
    if err != nil {
        tx.Rollback()
        return err
    }
    
    return tx.Commit().Error
}
```

### 3. Business Logic Integration

Create a custom business handler that integrates with your cash-in logic:

```go
type CashInBusinessHandler struct {
    dbAdapter *DatabaseIntegrationAdapter
    validator *ottopay.Validator
}

func (h *CashInBusinessHandler) HandleInquiry(ctx context.Context, req ottopay.InquiryRequest) (ottopay.InquiryData, error) {
    // Validate request
    if err := h.validator.ValidateInquiryRequest(req); err != nil {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: err.Error()}, nil
    }
    
    // Generate VA number from company code + customer number
    vaNumber := req.CompanyCode + req.CustomerNumber
    
    // Get virtual account from database
    va, err := h.dbAdapter.GetVirtualAccount(ctx, vaNumber)
    if err != nil {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: "Virtual Account not found"}, nil
    }
    
    // Check if VA is active and not expired
    if va.Status != "ACTIVE" {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: "Virtual Account is not active"}, nil
    }
    
    if time.Now().After(va.ExpiresAt) {
        return ottopay.InquiryData{IsValid: false, ErrorMessage: "Virtual Account has expired"}, nil
    }
    
    return ottopay.InquiryData{
        CustomerName:   va.CustomerName,
        TotalAmount:    va.Amount,
        CurrencyCode:   ottopay.CurrencyIDR,
        SubCompany:     ottopay.DefaultSubCompany,
        AdditionalData: "",
        IsValid:        true,
    }, nil
}

func (h *CashInBusinessHandler) HandlePayment(ctx context.Context, req ottopay.PaymentRequest) (ottopay.PaymentData, error) {
    // Validate request
    if err := h.validator.ValidatePaymentRequest(req); err != nil {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: err.Error()}, nil
    }
    
    // Parse amounts
    paidAmount, _ := strconv.ParseFloat(req.PaidAmount, 64)
    totalAmount, _ := strconv.ParseFloat(req.TotalAmount, 64)
    
    // Generate VA number
    vaNumber := req.CompanyCode + req.CustomerNumber
    
    // Get virtual account
    va, err := h.dbAdapter.GetVirtualAccount(ctx, vaNumber)
    if err != nil {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Virtual Account not found"}, nil
    }
    
    // Validate payment amount
    if paidAmount != va.Amount {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Payment amount does not match bill amount"}, nil
    }
    
    // Check for duplicate payment
    if va.Status == "PAID" {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Payment already processed"}, nil
    }
    
    // Process payment
    err = h.dbAdapter.UpdateVirtualAccountStatus(ctx, vaNumber, "PAID")
    if err != nil {
        return ottopay.PaymentData{IsSuccess: false, ErrorMessage: "Payment processing failed"}, nil
    }
    
    return ottopay.PaymentData{
        IsSuccess:      true,
        CustomerName:   va.CustomerName,
        TotalAmount:    totalAmount,
        PaidAmount:     paidAmount,
        CurrencyCode:   req.CurrencyCode,
        AdditionalData: "",
    }, nil
}
```

### 4. Route Setup

Add OttoPay routes to your existing Fiber application:

```go
func SetupOttoPayRoutes(app *fiber.App, cashinRepo usecase.CashinTranscationRepo, db *gorm.DB) {
    // Create database adapter
    dbAdapter := NewDatabaseIntegrationAdapter(cashinRepo, db)
    
    // Create OttoPay configuration
    config := ottopay.NewConfigBuilder().
        WithJWTSecret(os.Getenv("OTTOPAY_JWT_SECRET")).
        WithTokenDuration(24 * time.Hour).
        WithAllowedIPs([]string{"************", "*************"}).
        WithIPAuth(true).
        WithRequestTimeout(3 * time.Second).
        Build()
    
    // Create business handler
    businessHandler := &CashInBusinessHandler{
        dbAdapter: dbAdapter,
        validator: ottopay.NewValidator(),
    }
    config.BusinessHandler = businessHandler
    
    // Create OttoPay service
    ottoPayService := ottopay.NewService().Setup(config)
    
    // Setup OttoPay routes under /ottopay prefix
    ottopay.SetupRoutes(app, ottoPayService, config)
    
    log.Println("OttoPay routes setup complete")
}
```

### 5. Callback Processing

Add callback processing similar to existing providers:

```go
// Add to your callback handler
func (h *HandlerCallback) CallbackOttoPay(c *fiber.Ctx) error {
    ctx := c.UserContext()
    
    // Get authorization token
    token := strings.TrimPrefix(c.Get("Authorization"), "Bearer ")
    if token == "" {
        return fiber.NewError(fiber.StatusUnauthorized, "Missing authorization token")
    }
    
    // Parse request body
    var req interface{}
    err := c.BodyParser(&req)
    if err != nil {
        return fiber.NewError(fiber.StatusBadRequest, "Invalid request body")
    }
    
    // Process callback
    err = h.PushCallbackOttoPayCashin(ctx, token, req, constants.TrxVirtualAccount)
    if err != nil {
        log.Printf("OttoPay callback error: %v", err)
        return fiber.NewError(fiber.StatusInternalServerError, "Callback processing failed")
    }
    
    return c.JSON(fiber.Map{"status": "ok"})
}

func (h *HandlerCallback) PushCallbackOttoPayCashin(ctx context.Context, token string, req interface{}, paymentType string) error {
    // Validate token and process callback
    // Similar to PushCallbackXenditCashin, PushCallbackXfersCashin, etc.
    
    switch paymentType {
    case constants.TrxVirtualAccount:
        // Handle VA callback
        var callback ottopay.PaymentResponse
        reqByte, _ := json.Marshal(req)
        err := json.Unmarshal(reqByte, &callback)
        if err != nil {
            return fmt.Errorf("failed to unmarshal OttoPay callback: %w", err)
        }
        
        // Process the callback asynchronously
        go h.CallbackOttoPayCashinTransactionProcess(ctx, callback.RequestID, callback.CompanyCode+callback.CustomerNumber, paymentType, reqByte)
        
    default:
        return fmt.Errorf("unsupported OttoPay payment type: %s", paymentType)
    }
    
    return nil
}
```

### 6. Environment Configuration

Add OttoPay configuration to your environment:

```bash
# OttoPay Configuration
OTTOPAY_JWT_SECRET=your-ottopay-jwt-secret-key
OTTOPAY_TOKEN_DURATION=24h
OTTOPAY_ENABLE_IP_AUTH=true
OTTOPAY_ALLOWED_IPS=************,*************
OTTOPAY_REQUEST_TIMEOUT=3s

# Bank Prefixes
OTTOPAY_PREFIX_MANDIRI=71101
OTTOPAY_PREFIX_BRI=15772
OTTOPAY_PREFIX_BINA=98192
OTTOPAY_PREFIX_BNI=8428
```

### 7. Virtual Account Generation

When creating cash-in transactions, generate VA numbers using OttoPay format:

```go
func GenerateOttoPayVA(channelType, customerPhone string) string {
    // Get prefix for the channel
    prefix := ottopay.BankPrefixes[channelType]
    
    // Use customer phone as customer number (max 11 digits)
    customerNumber := customerPhone
    if len(customerNumber) > 11 {
        customerNumber = customerNumber[len(customerNumber)-11:]
    }
    
    return prefix + customerNumber
}

// Example usage in cash-in creation
func CreateCashInWithOttoPay(req CreateCashInRequest) {
    // Create cash-in transaction
    cashIn := &domain.CashInTransactions{
        CustomerPhone: req.CustomerPhone,
        CustomerName:  req.CustomerName,
        Total:         req.Amount,
        // ... other fields
    }
    
    // Generate VA number for OttoPay
    if req.PaymentChannel == "OTTOPAY_MANDIRI" {
        vaNumber := GenerateOttoPayVA(ottopay.ChannelMandiri, req.CustomerPhone)
        cashIn.VirtualAccount = vaNumber
    }
    
    // Save to database
    // ...
}
```

## Testing

1. **Unit Tests**: Test the database adapter and business handler
2. **Integration Tests**: Test the complete flow with mock OttoPay callbacks
3. **Manual Testing**: Use the OttoPay simulator for end-to-end testing

## Monitoring

The OttoPay package includes built-in logging and monitoring. All transactions are logged with:
- Request/response details
- Performance metrics
- Error tracking
- Audit trails

## Security

- JWT token validation for all requests
- IP whitelisting for OttoPay servers
- Request signature validation (optional)
- Input validation and sanitization

This integration follows the same patterns as existing payment providers and seamlessly integrates with the cash-in transaction flow.
