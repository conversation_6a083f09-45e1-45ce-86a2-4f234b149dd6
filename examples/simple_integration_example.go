package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"

	"repo.nusatek.id/nusatek/ottopay"
)

// SimpleCashInTransaction represents a simplified cash-in transaction
type SimpleCashInTransaction struct {
	ID             int       `json:"id"`
	InvoiceNumber  string    `json:"invoice_number"`
	CustomerPhone  string    `json:"customer_phone"`
	CustomerName   string    `json:"customer_name"`
	Amount         float64   `json:"amount"`
	AdminFee       float64   `json:"admin_fee"`
	Status         string    `json:"status"`
	PaymentStatus  string    `json:"payment_status"`
	VirtualAccount string    `json:"virtual_account"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	ExpiresAt      time.Time `json:"expires_at"`
}

// SimpleRepository simulates a database repository
type SimpleRepository struct {
	transactions map[string]*SimpleCashInTransaction
}

// NewSimpleRepository creates a new simple repository
func NewSimpleRepository() *SimpleRepository {
	return &SimpleRepository{
		transactions: make(map[string]*SimpleCashInTransaction),
	}
}

// GetByVANumber gets transaction by VA number
func (r *SimpleRepository) GetByVANumber(vaNumber string) (*SimpleCashInTransaction, error) {
	for _, tx := range r.transactions {
		if tx.VirtualAccount == vaNumber {
			return tx, nil
		}
	}
	return nil, fmt.Errorf("transaction not found")
}

// GetByInvoiceNumber gets transaction by invoice number
func (r *SimpleRepository) GetByInvoiceNumber(invoiceNumber string) (*SimpleCashInTransaction, error) {
	if tx, exists := r.transactions[invoiceNumber]; exists {
		return tx, nil
	}
	return nil, fmt.Errorf("transaction not found")
}

// UpdateStatus updates transaction status
func (r *SimpleRepository) UpdateStatus(invoiceNumber, status, paymentStatus string) error {
	if tx, exists := r.transactions[invoiceNumber]; exists {
		tx.Status = status
		tx.PaymentStatus = paymentStatus
		tx.UpdatedAt = time.Now()
		return nil
	}
	return fmt.Errorf("transaction not found")
}

// CreateTransaction creates a new transaction
func (r *SimpleRepository) CreateTransaction(tx *SimpleCashInTransaction) error {
	r.transactions[tx.InvoiceNumber] = tx
	return nil
}

// SimpleDatabaseAdapter adapts the simple repository to OttoPay's interface
type SimpleDatabaseAdapter struct {
	repo *SimpleRepository
}

// NewSimpleDatabaseAdapter creates a new adapter
func NewSimpleDatabaseAdapter(repo *SimpleRepository) *SimpleDatabaseAdapter {
	return &SimpleDatabaseAdapter{repo: repo}
}

// ValidateUserCredentials validates OttoPay credentials
func (d *SimpleDatabaseAdapter) ValidateUserCredentials(ctx context.Context, username, password string) (*ottopay.UserInfo, error) {
	// Simple validation - in real implementation, check against your user database
	if username == "ottopay" && password == "ac710qf!" {
		return &ottopay.UserInfo{
			ID:       "ottopay-user-id",
			Username: username,
			IsActive: true,
			Company:  "OTTOPAY",
		}, nil
	}
	return nil, fmt.Errorf("invalid credentials")
}

// GetVirtualAccount retrieves virtual account information
func (d *SimpleDatabaseAdapter) GetVirtualAccount(ctx context.Context, vaNumber string) (*ottopay.VirtualAccount, error) {
	// Get transaction by VA number
	tx, err := d.repo.GetByVANumber(vaNumber)
	if err != nil {
		return nil, fmt.Errorf("virtual account not found")
	}

	// Extract prefix and customer number
	prefix, customerNumber := d.extractPrefixAndCustomer(vaNumber)

	// Convert to OttoPay VirtualAccount
	va := &ottopay.VirtualAccount{
		ID:             fmt.Sprintf("va-%d", tx.ID),
		VANumber:       vaNumber,
		CompanyCode:    prefix,
		CustomerNumber: customerNumber,
		ChannelType:    d.getChannelTypeFromPrefix(prefix),
		CustomerName:   tx.CustomerName,
		Amount:         tx.Amount + tx.AdminFee,
		Status:         d.mapStatusToVAStatus(tx.Status, tx.PaymentStatus),
		CreatedAt:      tx.CreatedAt,
		UpdatedAt:      tx.UpdatedAt,
		ExpiresAt:      tx.ExpiresAt,
	}

	return va, nil
}

// UpdateVirtualAccountStatus updates VA status
func (d *SimpleDatabaseAdapter) UpdateVirtualAccountStatus(ctx context.Context, vaNumber, status string) error {
	// Find transaction by VA number
	tx, err := d.repo.GetByVANumber(vaNumber)
	if err != nil {
		return err
	}

	// Map OttoPay status to internal status
	var txStatus, paymentStatus string
	switch status {
	case "PAID":
		txStatus = "PAID"
		paymentStatus = "SUCCESS"
	case "FAILED":
		txStatus = "FAILED"
		paymentStatus = "FAILED"
	case "EXPIRED":
		txStatus = "EXPIRED"
		paymentStatus = "EXPIRED"
	default:
		txStatus = "PENDING"
		paymentStatus = "PENDING"
	}

	// Update transaction
	return d.repo.UpdateStatus(tx.InvoiceNumber, txStatus, paymentStatus)
}

// Helper methods
func (d *SimpleDatabaseAdapter) extractPrefixAndCustomer(vaNumber string) (string, string) {
	for _, prefix := range ottopay.BankPrefixes {
		if strings.HasPrefix(vaNumber, prefix) {
			return prefix, vaNumber[len(prefix):]
		}
	}
	return "", vaNumber
}

func (d *SimpleDatabaseAdapter) getChannelTypeFromPrefix(prefix string) string {
	for channelType, channelPrefix := range ottopay.BankPrefixes {
		if channelPrefix == prefix {
			return channelType
		}
	}
	return ""
}

func (d *SimpleDatabaseAdapter) mapStatusToVAStatus(status, paymentStatus string) string {
	switch {
	case paymentStatus == "SUCCESS":
		return "PAID"
	case paymentStatus == "FAILED":
		return "FAILED"
	case paymentStatus == "EXPIRED":
		return "EXPIRED"
	case status == "PENDING":
		return "ACTIVE"
	default:
		return "INACTIVE"
	}
}

// Implement other required methods with simple implementations
func (d *SimpleDatabaseAdapter) GetUserByID(ctx context.Context, userID string) (*ottopay.UserInfo, error) {
	return &ottopay.UserInfo{ID: userID, Username: "ottopay", IsActive: true}, nil
}

func (d *SimpleDatabaseAdapter) CreateVirtualAccount(ctx context.Context, va *ottopay.VirtualAccount) error {
	return nil // Not needed for this example
}

func (d *SimpleDatabaseAdapter) CreateInquiryLog(ctx context.Context, log *ottopay.InquiryLog) error {
	fmt.Printf("Inquiry Log: %+v\n", log)
	return nil
}

func (d *SimpleDatabaseAdapter) CreatePaymentLog(ctx context.Context, log *ottopay.PaymentLog) error {
	fmt.Printf("Payment Log: %+v\n", log)
	return nil
}

func (d *SimpleDatabaseAdapter) GetTransactionByRequestID(ctx context.Context, requestID string) (*ottopay.Transaction, error) {
	return nil, fmt.Errorf("not found") // Simulate not found for idempotency
}

func (d *SimpleDatabaseAdapter) UpdateTransactionStatus(ctx context.Context, requestID, status string) error {
	return nil
}

func (d *SimpleDatabaseAdapter) GetCompanyByCode(ctx context.Context, companyCode string) (*ottopay.Company, error) {
	return &ottopay.Company{ID: "1", Code: companyCode, Name: "Test Company", IsActive: true}, nil
}

func (d *SimpleDatabaseAdapter) GetProviderConfig(ctx context.Context, providerID string) (*ottopay.ProviderConfig, error) {
	return &ottopay.ProviderConfig{ID: providerID, ProviderName: "OTTOPAY", IsActive: true}, nil
}

// SimpleIntegrationExample demonstrates a complete integration
func SimpleIntegrationExample() {
	// Create repository and adapter
	repo := NewSimpleRepository()
	dbAdapter := NewSimpleDatabaseAdapter(repo)

	// Create some sample transactions
	sampleTx := &SimpleCashInTransaction{
		ID:             1,
		InvoiceNumber:  "INV001",
		CustomerPhone:  "***********",
		CustomerName:   "John Doe",
		Amount:         150000,
		AdminFee:       2500,
		Status:         "PENDING",
		PaymentStatus:  "PENDING",
		VirtualAccount: "****************", // Mandiri prefix + phone
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		ExpiresAt:      time.Now().Add(24 * time.Hour),
	}
	repo.CreateTransaction(sampleTx)

	// Create OttoPay configuration
	config := ottopay.NewConfigBuilder().
		WithJWTSecret("simple-example-secret").
		WithTokenDuration(24 * time.Hour).
		WithPort("8080").
		WithAllowedIPs([]string{"************", "*************"}).
		WithIPAuth(false). // Disable for testing
		WithRequestTimeout(3 * time.Second).
		Build()

	// Create business handler
	businessHandler := ottopay.NewDefaultBusinessHandler(dbAdapter, config)
	config.BusinessHandler = businessHandler

	// Create OttoPay service
	ottoPayService := ottopay.NewService().Setup(config)

	// Create Fiber app
	app := fiber.New()

	// Add a simple route to create transactions
	app.Post("/create-transaction", func(c *fiber.Ctx) error {
		var req struct {
			CustomerPhone string  `json:"customer_phone"`
			CustomerName  string  `json:"customer_name"`
			Amount        float64 `json:"amount"`
			ChannelType   string  `json:"channel_type"`
		}

		if err := c.BodyParser(&req); err != nil {
			return c.Status(400).JSON(fiber.Map{"error": "Invalid request"})
		}

		// Generate VA number
		prefix := ottopay.BankPrefixes[req.ChannelType]
		customerNumber := req.CustomerPhone
		if len(customerNumber) > 11 {
			customerNumber = customerNumber[len(customerNumber)-11:]
		}
		vaNumber := prefix + customerNumber

		// Create transaction
		tx := &SimpleCashInTransaction{
			ID:             len(repo.transactions) + 1,
			InvoiceNumber:  fmt.Sprintf("INV%03d", len(repo.transactions)+1),
			CustomerPhone:  req.CustomerPhone,
			CustomerName:   req.CustomerName,
			Amount:         req.Amount,
			AdminFee:       2500,
			Status:         "PENDING",
			PaymentStatus:  "PENDING",
			VirtualAccount: vaNumber,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
			ExpiresAt:      time.Now().Add(24 * time.Hour),
		}

		err := repo.CreateTransaction(tx)
		if err != nil {
			return c.Status(500).JSON(fiber.Map{"error": "Failed to create transaction"})
		}

		return c.JSON(fiber.Map{
			"invoice_number":  tx.InvoiceNumber,
			"virtual_account": tx.VirtualAccount,
			"amount":          tx.Amount + tx.AdminFee,
			"expires_at":      tx.ExpiresAt,
		})
	})

	// Setup OttoPay routes
	ottopay.SetupRoutes(app, ottoPayService, config)

	// Add a route to check transaction status
	app.Get("/transaction/:invoice", func(c *fiber.Ctx) error {
		invoice := c.Params("invoice")
		tx, err := repo.GetByInvoiceNumber(invoice)
		if err != nil {
			return c.Status(404).JSON(fiber.Map{"error": "Transaction not found"})
		}
		return c.JSON(tx)
	})

	log.Println("Simple OttoPay integration example starting on :8080")
	log.Println("Available endpoints:")
	log.Println("  POST /create-transaction - Create a new transaction")
	log.Println("  GET  /transaction/:invoice - Check transaction status")
	log.Println("  POST /ottopay/token - Get OttoPay token")
	log.Println("  POST /ottopay/inquiry - OttoPay inquiry")
	log.Println("  POST /ottopay/payment - OttoPay payment")
	log.Println("  GET  /health - Health check")

	log.Fatal(app.Listen(":8080"))
}
