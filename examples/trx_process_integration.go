// This file shows how to integrate Otto<PERSON>ay into the existing trx_process.go
// Add these methods to your cashinTransactionUsecase struct

package usecase

import (
	"context"
	"errors"
	"fmt"
	"time"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils/constants"

	"repo.nusatek.id/nusatek/ottopay"
)

// Add OttoPay to the main ProcessCashinTransaction switch statement
func (u *cashinTransactionUsecase) ProcessCashinTransactionWithOttoPay(ctx context.Context, req entity.CashinTransactionReq) (entity.CashinTransactionRes, error) {
	// ... existing validation code ...

	// Get payment provider, channel, and mapping (existing code)
	paymentProvider, err := u.paymentProviderRepo.GetPaymentProviderByID(ctx, req.PaymentProviderID)
	if err != nil {
		return entity.CashinTransactionRes{}, err
	}

	paymentChannel, err := u.paymentChannelRepo.GetPaymentChannelByID(ctx, req.PaymentChannelID)
	if err != nil {
		return entity.CashinTransactionRes{}, err
	}

	companyPaymentProviderChannelMapping, err := u.companyPaymentProviderChannelMappingRepo.GetCompanyPaymentProviderChannelMappingByCompanyIDAndPaymentProviderIDAndPaymentChannelID(ctx, req.CompanyID, req.PaymentProviderID, req.PaymentChannelID)
	if err != nil {
		return entity.CashinTransactionRes{}, err
	}

	// Add OttoPay to the provider switch statement
	switch paymentProvider.Name {
	case constants.ProviderXfers:
		return u.processXfersTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderXendit:
		return u.processXenditTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderNicePay:
		return u.processNicePayTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderOttocash:
		return u.processOttocashTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	case constants.ProviderOttoPay: // Add this case
		return u.processOttoPayTransaction(ctx, req, paymentProvider, paymentChannel, companyPaymentProviderChannelMapping)
	default:
		return entity.CashinTransactionRes{}, errors.New("payment provider not supported")
	}
}

// Add this new method to handle OttoPay transactions
func (u *cashinTransactionUsecase) processOttoPayTransaction(
	ctx context.Context,
	req entity.CashinTransactionReq,
	paymentProvider *domain.PaymentProviders,
	paymentChannel *domain.PaymentChannels,
	mapping *domain.CompanyPaymentProviderChannelMappings,
) (entity.CashinTransactionRes, error) {

	// Determine OttoPay channel type based on payment channel code
	var channelType string
	switch paymentChannel.Code {
	case "OTTOPAY_MANDIRI":
		channelType = ottopay.ChannelMandiri
	case "OTTOPAY_BRI":
		channelType = ottopay.ChannelBRI
	case "OTTOPAY_BINA":
		channelType = ottopay.ChannelBINA
	case "OTTOPAY_BNI":
		channelType = ottopay.ChannelBNI
	default:
		return entity.CashinTransactionRes{}, errors.New("unsupported OttoPay channel")
	}

	// Generate VA number using OttoPay format
	prefix := ottopay.BankPrefixes[channelType]
	customerNumber := req.CustomerPhone
	if len(customerNumber) > 11 {
		customerNumber = customerNumber[len(customerNumber)-11:]
	}
	vaNumber := prefix + customerNumber

	// Generate invoice number (use existing method)
	invoiceNumber := u.generateInvoiceNumber()

	// Create cash_in_transaction record
	cashInTransaction := &domain.CashInTransactions{
		CompanyID:                              req.CompanyID,
		CompanyProductID:                       req.CompanyProductID,
		InvoiceNumber:                          invoiceNumber,
		CustomerName:                           req.CustomerName,
		CustomerPhone:                          req.CustomerPhone,
		CustomerEmail:                          req.CustomerEmail,
		PaymentProviderID:                      paymentProvider.ID,
		PaymentChannelID:                       paymentChannel.ID,
		CompanyPaymentProviderChannelMappingID: mapping.ID,
		Total:                                  req.Total,
		AdminFee:                               req.AdminFee,
		Discount:                               req.Discount,
		Voucher:                                req.Voucher,
		PgDeliveryFee:                          req.PgDeliveryFee,
		PaymentStatus:                          constants.PaymentPending,
		Status:                                 constants.CashInStatusPending,
		VirtualAccount:                         vaNumber,
		ExpiredAt:                              &req.ExpiredAt,
		CreatedAt:                              time.Now(),
		UpdatedAt:                              time.Now(),
	}

	// Begin transaction
	tx := u.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Save cash_in_transaction to database
	err := u.cashinRepo.CreateCashinTransaction(ctx, tx, cashInTransaction)
	if err != nil {
		tx.Rollback()
		return entity.CashinTransactionRes{}, fmt.Errorf("failed to create cash_in_transaction: %w", err)
	}

	// Create transaction items
	for _, item := range req.Items {
		cashInItem := &domain.CashInTransactionItems{
			CashInTransactionID: cashInTransaction.ID,
			ProductName:         item.ProductName,
			ProductCode:         item.ProductCode,
			Quantity:            item.Quantity,
			Price:               item.Price,
			Status:              constants.CashInItemStatusPending,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
		}

		err = u.cashinRepo.CreateCashinTransactionItem(ctx, tx, cashInItem)
		if err != nil {
			tx.Rollback()
			return entity.CashinTransactionRes{}, fmt.Errorf("failed to create cash_in_transaction_item: %w", err)
		}
	}

	// Create transaction history
	history := &domain.CashInTransactionHistories{
		CashInTransactionID: cashInTransaction.ID,
		Description:         "OttoPay Virtual Account transaction created",
		PaymentStatus:       constants.PaymentPending,
		Status:              constants.CashInStatusPending,
		VirtualAccount:      vaNumber,
		ProviderId:          paymentProvider.ID,
		ChannelId:           paymentChannel.ID,
		PgReferenceInformation: fmt.Sprintf(`{
			"va_number": "%s",
			"channel_type": "%s",
			"bank_prefix": "%s",
			"customer_number": "%s",
			"provider": "ottopay"
		}`, vaNumber, channelType, prefix, customerNumber),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = u.cashinRepo.CreateCashinTransactionHistory(ctx, tx, history)
	if err != nil {
		tx.Rollback()
		return entity.CashinTransactionRes{}, fmt.Errorf("failed to create transaction history: %w", err)
	}

	// Commit transaction
	err = tx.Commit().Error
	if err != nil {
		return entity.CashinTransactionRes{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Calculate grand total
	grandTotal := cashInTransaction.Total + cashInTransaction.AdminFee + cashInTransaction.PgDeliveryFee - cashInTransaction.Discount - cashInTransaction.Voucher

	// Return response following the same pattern as other providers
	return entity.CashinTransactionRes{
		ID:                  cashInTransaction.ID,
		InvoiceNumber:       cashInTransaction.InvoiceNumber,
		VirtualAccount:      vaNumber,
		Total:               cashInTransaction.Total,
		AdminFee:            cashInTransaction.AdminFee,
		Discount:            cashInTransaction.Discount,
		Voucher:             cashInTransaction.Voucher,
		PgDeliveryFee:       cashInTransaction.PgDeliveryFee,
		GrandTotal:          grandTotal,
		ExpiredAt:           cashInTransaction.ExpiredAt,
		PaymentProviderName: paymentProvider.Name,
		PaymentChannelName:  paymentChannel.Name,
		Status:              cashInTransaction.Status,
		PaymentStatus:       cashInTransaction.PaymentStatus,
		CreatedAt:           cashInTransaction.CreatedAt,
		UpdatedAt:           cashInTransaction.UpdatedAt,
	}, nil
}

// Helper method to generate invoice number (if not already exists)
func (u *cashinTransactionUsecase) generateInvoiceNumber() string {
	// Use existing invoice generation logic or implement new one
	// This should follow your existing pattern
	timestamp := time.Now().Format("**************")
	return fmt.Sprintf("OTTOPAY%s", timestamp)
}

// Add method to get cash_in_transaction by VA number (if not exists)
func (u *cashinTransactionUsecase) GetCashinTransactionByVirtualAccount(ctx context.Context, vaNumber string) (*domain.CashInTransactions, error) {
	return u.cashinRepo.GetCashinTransactionByVirtualAccount(ctx, vaNumber)
}

// Example of how to add OttoPay configuration to your usecase
type OttoPayConfig struct {
	JWTSecret     string
	TokenDuration time.Duration
	AllowedIPs    []string
	EnableIPAuth  bool
}

// Add OttoPay service to your usecase struct (optional)
type cashinTransactionUsecaseWithOttoPay struct {
	*cashinTransactionUsecase
	ottoPayService ottopay.OttoPayService
	ottoPayConfig  OttoPayConfig
}

// Initialize OttoPay service
func (u *cashinTransactionUsecaseWithOttoPay) initOttoPayService() error {
	config := ottopay.NewConfigBuilder().
		WithJWTSecret(u.ottoPayConfig.JWTSecret).
		WithTokenDuration(u.ottoPayConfig.TokenDuration).
		WithAllowedIPs(u.ottoPayConfig.AllowedIPs).
		WithIPAuth(u.ottoPayConfig.EnableIPAuth).
		WithRequestTimeout(3 * time.Second).
		Build()

	// Create database adapter (implement based on your needs)
	dbAdapter := NewOttoPayDatabaseAdapter(u.cashinRepo, u.db)
	businessHandler := ottopay.NewDefaultBusinessHandler(dbAdapter, config)
	config.BusinessHandler = businessHandler

	u.ottoPayService = ottopay.NewService().Setup(config)
	return nil
}

// Example of how to validate OttoPay callback token
func (u *cashinTransactionUsecaseWithOttoPay) ValidateOttoPayToken(ctx context.Context, token string) (*ottopay.TokenClaims, error) {
	if u.ottoPayService == nil {
		return nil, errors.New("OttoPay service not initialized")
	}
	return u.ottoPayService.ValidateToken(ctx, token)
}
